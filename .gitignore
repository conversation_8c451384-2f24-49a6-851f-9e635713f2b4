/node_modules
/public/hot
/public/storage
/storage/*.key
/storage/debugbar
/storage/tenantbozovic/*
/vendor
/.idea
/.vscode
/.vagrant
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log
.env
.env.dusk.local
.env.dusk.staging
.phpunit.result.cache
/public/js/*
/public/css/*
/public/mix-manifest.json
/public/css/app.css
/public/css/editor.css
/public/css/preview.css
/storage/framework/disposable_domains.json
/public/sitemap.xml
/public/js/admin/*
phpunit.dusk.xml
phpstan.neon
# Ignore .DS_Store files (mac specific) in any path
**/.DS_Store
# Ignore PhpStorm temporary files
*~
QWEN.md
GEMINI.md
