@extends('tenant.layouts.common.master')

@section('title', 'Branding')

@section('content')
    @php
        $activeTab = request('tab', 'general');
    @endphp

    <h1 class="pb-4">Branding</h1>

    <div class="row">
        <div class="col-lg-6">
            <form method="POST" action="{{ route('tenant.branding.update') }}" enctype="multipart/form-data">
                @csrf
                @method('PUT')

                <input type="hidden" name="tab" id="activeTabInput" value="{{ $activeTab }}">

                {{-- NAV TABS --}}
                <ul class="nav nav-tabs" id="brandingTabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link {{ $activeTab === 'general' ? 'active' : '' }}"
                           id="general-tab" data-toggle="tab" href="#general" role="tab"
                           aria-controls="general" aria-selected="{{ $activeTab === 'general' ? 'true' : 'false' }}">
                            <PERSON>s<PERSON><PERSON>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ $activeTab === 'notifications' ? 'active' : '' }}"
                           id="notifications-tab" data-toggle="tab" href="#notifications" role="tab"
                           aria-controls="notifications" aria-selected="{{ $activeTab === 'notifications' ? 'true' : 'false' }}">
                            Obavijesti
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ $activeTab === 'documents' ? 'active' : '' }}"
                           id="documents-tab" data-toggle="tab" href="#documents" role="tab"
                           aria-controls="documents" aria-selected="{{ $activeTab === 'documents' ? 'true' : 'false' }}">
                            Dokumenti
                        </a>
                    </li>
                </ul>

                {{-- TAB PANES --}}
                <div class="tab-content pt-3">

                    {{-- GENERAL --}}
                    <div class="tab-pane {{ $activeTab === 'general' ? 'show active' : '' }}"
                         id="general" role="tabpanel" aria-labelledby="general-tab">
                        <div class="form-group">
                            <label for="appName">Naziv aplikacije <span class="text-danger">*</span></label>
                            <input id="appName" type="text"
                                   class="form-control @error('appName') is-invalid @enderror"
                                   name="appName" placeholder="Upiši naziv aplikacije..."
                                   value="{{ old('appName', $tenant->appName) }}" required autocomplete="off">
                            @error('appName')
                            <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                            @enderror
                        </div>
                    </div>

                    {{-- NOTIFICATIONS --}}
                    <div class="tab-pane {{ $activeTab === 'notifications' ? 'show active' : '' }}"
                         id="notifications" role="tabpanel" aria-labelledby="notifications-tab">
                        <div class="form-group mt-3">
                            <label for="emailFromName">Ime pošiljatelja za email obavijesti <span class="text-danger">*</span></label>
                            <input id="emailFromName" type="text"
                                   class="form-control @error('emailFromName') is-invalid @enderror"
                                   name="emailFromName" placeholder="Upiši ime pošiljatelja..."
                                   value="{{ old('emailFromName', $tenant->emailFromName) }}" required autocomplete="off">
                            @error('emailFromName')
                            <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                            @enderror
                        </div>

                        <div class="form-group mt-3">
                            <label for="emailFromAddress">E-pošta pošiljatelja <span class="text-danger">*</span></label>
                            <input id="emailFromAddress" type="email"
                                   class="form-control @error('emailFromAddress') is-invalid @enderror"
                                   name="emailFromAddress" placeholder="Upiši e-poštu pošiljatelja..."
                                   value="{{ old('emailFromAddress', $tenant->emailFromAddress) }}" required autocomplete="off">
                            @error('emailFromAddress')
                            <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                            @enderror
                        </div>
                    </div>

                    {{-- DOCUMENTS --}}
                    <div class="tab-pane {{ $activeTab === 'documents' ? 'show active' : '' }}"
                         id="documents" role="tabpanel" aria-labelledby="documents-tab">
                        @php
                            $headerParam = request('header');
                            $footerParam = request('footer');
                            $headerOpen = ($headerParam === 'open');
                            $footerOpen = ($footerParam === 'open');
                            // If neither is explicitly open, default to header open
                            $defaultHeaderOpen = (!$headerOpen && !$footerOpen) ? true : false;
                        @endphp

                        <div class="accordion" id="documentsAccordion">
                            {{-- HEADER --}}
                            <div class="card">
                                <div class="card-header" id="headerHeading">
                                    <h2 class="mb-0">
                                        <button class="btn btn-link btn-block text-left{{ ($headerOpen || ($defaultHeaderOpen && !$footerOpen)) ? '' : ' collapsed' }}" type="button" data-toggle="collapse" data-target="#headerCollapse" aria-expanded="{{ ($headerOpen || ($defaultHeaderOpen && !$footerOpen)) ? 'true' : 'false' }}" aria-controls="headerCollapse">
                                            Zaglavlje
                                        </button>
                                    </h2>
                                </div>

                                <div id="headerCollapse" class="collapse{{ ($headerOpen || ($defaultHeaderOpen && !$footerOpen)) ? ' show' : '' }}" aria-labelledby="headerHeading" data-parent="#documentsAccordion">
                                    <div class="card-body">
                                        {{-- Logo --}}
                                        <div class="form-group">
                                            <label for="headerLogo">Logotip</label>
                                            @if($tenant->headerLogoPath)
                                                <div id="headerLogo-preview" class="mb-3">
                                                    <input type="hidden" name="headerLogoPath" value="{{ $tenant->headerLogoPath }}">
                                                    <img src="{{ tenant_asset($tenant->headerLogoPath) }}" alt="Trenutni logo"
                                                         class="img-thumbnail" style="max-height:150px;">
                                                    <div class="mt-1">
                                                        <a id="removeHeaderLogo" class="ml-2 text-danger" href="#"><i class="fas fa-trash"></i> Ukloni logotip</a>
                                                    </div>
                                                </div>
                                            @endif
                                            <input id="headerLogo" type="file"
                                                   class="form-control-file @error('headerLogo') is-invalid @enderror"
                                                   name="headerLogo">
                                            @error('headerLogo')
                                            <span class="invalid-feedback d-block" role="alert"><strong>{{ $message }}</strong></span>
                                            @enderror
                                        </div>

                                        {{-- Logo position --}}
                                        <div class="form-group">
                                            <label for="headerLogoPosition">Pozicija logotipa</label>
                                            <select id="headerLogoPosition" name="headerLogoPosition" class="form-control">
                                                <option value="left"   {{ old('headerLogoPosition', $tenant->headerLogoPosition ?? 'left')   == 'left'   ? 'selected' : '' }}>Lijevo</option>
                                                <option value="center" {{ old('headerLogoPosition', $tenant->headerLogoPosition ?? 'left')   == 'center' ? 'selected' : '' }}>Sredina</option>
                                                <option value="right"  {{ old('headerLogoPosition', $tenant->headerLogoPosition ?? 'left')   == 'right'  ? 'selected' : '' }}>Desno</option>
                                            </select>
                                        </div>

                                        {{-- Text --}}
                                        <div class="form-group">
                                            <label for="headerText">Tekst u zaglavlju</label>
                                            <textarea placeholder="Upiši tekst koji se prikazuje u zaglavlju stranice dokumenta..." id="headerText" name="headerText" rows="3"
                                                      class="form-control">{{ old('headerText', $tenant->headerText) }}</textarea>
                                        </div>

                                        {{-- Text position --}}
                                        <div class="form-group">
                                            <label for="headerTextPosition">Pozicija teksta</label>
                                            <select id="headerTextPosition" name="headerTextPosition" class="form-control">
                                                <option value="left"   {{ old('headerTextPosition', $tenant->headerTextPosition ?? 'right')  == 'left'   ? 'selected' : '' }}>Lijevo</option>
                                                <option value="center" {{ old('headerTextPosition', $tenant->headerTextPosition ?? 'right')  == 'center' ? 'selected' : '' }}>Sredina</option>
                                                <option value="right"  {{ old('headerTextPosition', $tenant->headerTextPosition ?? 'right')  == 'right'  ? 'selected' : '' }}>Desno</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {{-- FOOTER --}}
                            <div class="card">
                                <div class="card-header" id="footerHeading">
                                    <h2 class="mb-0">
                                        <button class="btn btn-link btn-block text-left{{ $footerOpen ? '' : ' collapsed' }}" type="button" data-toggle="collapse" data-target="#footerCollapse" aria-expanded="{{ $footerOpen ? 'true' : 'false' }}" aria-controls="footerCollapse">
                                            Podnožje
                                        </button>
                                    </h2>
                                </div>
                                <div id="footerCollapse" class="collapse{{ $footerOpen ? ' show' : '' }}" aria-labelledby="footerHeading" data-parent="#documentsAccordion">
                                    <div class="card-body">
                                        {{-- Logo --}}
                                        <div class="form-group">
                                            <label for="footerLogo">Logotip</label>
                                            @if($tenant->footerLogoPath)
                                                <div id="footerLogo-preview" class="mb-3">
                                                    <input type="hidden" name="footerLogoPath" value="{{ $tenant->footerLogoPath }}">
                                                    <img src="{{ tenant_asset($tenant->footerLogoPath) }}" alt="Trenutni logo"
                                                         class="img-thumbnail" style="max-height:150px;">
                                                    <div class="mt-1">
                                                        <a id="removeFooterLogo" class="ml-2 text-danger" href="#"><i class="fas fa-trash"></i> Ukloni logotip</a>
                                                    </div>
                                                </div>
                                            @endif
                                            <input id="footerLogo" type="file"
                                                   class="form-control-file @error('footerLogo') is-invalid @enderror"
                                                   name="footerLogo">
                                            @error('footerLogo')
                                            <span class="invalid-feedback d-block" role="alert"><strong>{{ $message }}</strong></span>
                                            @enderror
                                        </div>

                                        {{-- Logo position --}}
                                        <div class="form-group">
                                            <label for="footerLogoPosition">Pozicija logotipa</label>
                                            <select id="footerLogoPosition" name="footerLogoPosition" class="form-control">
                                                <option value="left"   {{ old('footerLogoPosition', $tenant->footerLogoPosition ?? 'center') == 'left'   ? 'selected' : '' }}>Lijevo</option>
                                                <option value="center" {{ old('footerLogoPosition', $tenant->footerLogoPosition ?? 'center') == 'center' ? 'selected' : '' }}>Sredina</option>
                                            </select>
                                        </div>

                                        {{-- Text --}}
                                        <div class="form-group">
                                            <label for="footerText">Tekst u podnožju</label>
                                            <textarea placeholder="Upiši tekst koji se prikazuje u podnožju stranice dokumenta..." id="footerText" name="footerText" rows="3"
                                                      class="form-control">{{ old('footerText', $tenant->footerText) }}</textarea>
                                        </div>

                                        {{-- Text position --}}
                                        <div class="form-group">
                                            <label for="footerTextPosition">Pozicija teksta</label>
                                            <select id="footerTextPosition" name="footerTextPosition" class="form-control">
                                                <option value="left"   {{ old('footerTextPosition', $tenant->footerTextPosition ?? 'left')  == 'left'   ? 'selected' : '' }}>Lijevo</option>
                                                <option value="center" {{ old('footerTextPosition', $tenant->footerTextPosition ?? 'left')  == 'center' ? 'selected' : '' }}>Sredina</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group mt-4">
                    <button type="submit" class="btn btn-block btn-primary">Spremi promjene</button>
                </div>
            </form>
        </div>
    </div>
@endsection

@push('styles')
    <style>
        .accordion .btn-link {
            text-decoration: none !important;
        }
        
        .accordion .btn-link:hover,
        .accordion .btn-link:focus {
            text-decoration: none !important;
        }
    </style>
@endpush

@push('scripts')
    <script>
        (function ($) {
            const priority = { header: ['left', 'center', 'right'], footer: ['left', 'center'] };

            function ensureUnique(section, changed) {
                const $logo = $('#' + section + 'LogoPosition');
                const $text = $('#' + section + 'TextPosition');
                const $changed = (changed === 'logo') ? $logo : $text;
                const $other   = (changed === 'logo') ? $text : $logo;

                const val = $changed.val();
                if (!val) return;

                if (val === $other.val()) {
                    const nextFree = priority[section].find(p => p !== val) || '';
                    $other.val(nextFree);
                }
            }

            $('#headerLogoPosition').on('change', () => ensureUnique('header', 'logo'));
            $('#headerTextPosition').on('change', () => ensureUnique('header', 'text'));
            $('#footerLogoPosition').on('change', () => ensureUnique('footer', 'logo'));
            $('#footerTextPosition').on('change', () => ensureUnique('footer', 'text'));

            ensureUnique('header', 'logo');
            ensureUnique('footer', 'logo');

            // Function to initialize accordion state
            function initializeAccordionState() {
                // Only apply accordion state logic if we're on the documents tab
                if ($('#documents').hasClass('active')) {
                    const urlParams = new URLSearchParams(window.location.search);
                    const headerParam = urlParams.get('header');
                    const footerParam = urlParams.get('footer');
                    
                    // Handle accordions based on URL parameters
                    if (headerParam === 'open') {
                        $('#headerCollapse').addClass('show');
                        $('#headerHeading .btn').removeClass('collapsed');
                    }
                    
                    if (footerParam === 'open') {
                        $('#footerCollapse').addClass('show');
                        $('#footerHeading .btn').removeClass('collapsed');
                    }
                }
            }

            $('#brandingTabs a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                const tabId = $(e.target).attr('href').substring(1);
                $('#activeTabInput').val(tabId);
                const url = new URL(window.location.href);
                url.searchParams.set('tab', tabId);
                
                // Remove accordion parameters when switching tabs
                if (tabId !== 'documents') {
                    url.searchParams.delete('header');
                    url.searchParams.delete('footer');
                }
                
                window.history.replaceState(null, '', url);
                
                // Initialize accordion state when switching to documents tab
                if (tabId === 'documents') {
                    initializeAccordionState();
                }
            });

            $('#removeHeaderLogo').on('click', function (e) {
                e.preventDefault();
                $('#headerLogo').val('');
                $('[name="headerLogoPath"]').val('');
                $(this).closest('#headerLogo-preview').remove();
            });

            $('#removeFooterLogo').on('click', function (e) {
                e.preventDefault();
                $('#footerLogo').val('');
                $('[name="footerLogoPath"]').val('');
                $(this).closest('#footerLogo-preview').remove();
            });

            // Accordion state management
            function updateAccordionUrlState() {
                const url = new URL(window.location.href);
                
                // Remove any existing accordion parameters
                url.searchParams.delete('header');
                url.searchParams.delete('footer');
                
                // Add parameters for open accordions
                if ($('#headerCollapse').hasClass('show')) {
                    url.searchParams.set('header', 'open');
                }
                if ($('#footerCollapse').hasClass('show')) {
                    url.searchParams.set('footer', 'open');
                }
                
                window.history.replaceState(null, '', url);
            }

            // Set initial accordion state based on URL parameters
            $(document).ready(function() {
                initializeAccordionState();
            });

            // Update URL when accordions are toggled
            $('#headerCollapse').on('shown.bs.collapse', function () {
                updateAccordionUrlState();
            }).on('hidden.bs.collapse', function () {
                updateAccordionUrlState();
            });

            $('#footerCollapse').on('shown.bs.collapse', function () {
                updateAccordionUrlState();
            }).on('hidden.bs.collapse', function () {
                updateAccordionUrlState();
            });

        })(jQuery);
    </script>
@endpush
