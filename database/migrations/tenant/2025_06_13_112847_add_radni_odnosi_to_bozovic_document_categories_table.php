<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        tenancy()->initialize('bozovic');
        // insert radni odnosi
        DB::table('document_categories')->insert([
            [
                'title' => 'Radni odnosi',
                'order_index' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);

        tenancy()->end();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        tenancy()->initialize('bozovic');
        DB::table('document_categories')->where('title', 'Radni odnosi')->delete();
        tenancy()->end();
    }
};
