<?php

namespace App\Observers;

use App\Models\DocumentTemplate;

class DocumentTemplateObserver
{
    /**
     * Handle the DocumentTemplate "created" event.
     *
     * @param DocumentTemplate $documentTemplate
     *
     * @return void
     */
    public function created(DocumentTemplate $documentTemplate)
    {
        if ($tenant = tenancy()->tenant) {
            \Artisan::call('es:sync', [
                'tenant' => $tenant->id
            ]);
        } else {
            \Artisan::call('es:sync');
        }
    }

    /**
     * Handle the DocumentTemplate "updated" event.
     *
     * @param DocumentTemplate $documentTemplate
     *
     * @return void
     */
    public function updated(DocumentTemplate $documentTemplate)
    {
        if ($tenant = tenancy()->tenant) {
            \Artisan::call('es:sync', [
                'tenant' => $tenant->id,
                'id' => $documentTemplate->id
            ]);
        } else {
            \Artisan::call('es:sync');
        }

        \Cache::tags(['template-search'])->flush();
    }

    /**
     * Handle the DocumentTemplate "saved" event.
     *
     * @param DocumentTemplate $documentTemplate
     *
     * @return void
     */
    public function saved(DocumentTemplate $documentTemplate)
    {
        if ($tenant = tenancy()->tenant) {
            \Artisan::call('es:sync', [
                'tenant' => $tenant->id
            ]);
        } else {
            \Artisan::call('es:sync');
        }
    }

    /**
     * Handle the DocumentTemplate "deleted" event.
     *
     * @param DocumentTemplate $documentTemplate
     *
     * @return void
     */
	public function deleted(DocumentTemplate $documentTemplate)
    {
        if ($tenant = tenancy()->tenant) {
            \Artisan::call('es:sync', [
                'tenant' => $tenant->id
            ]);
        } else {
            \Artisan::call('es:sync');
        }
    }

    /**
     * Handle the DocumentTemplate "restored" event.
     *
     * @param DocumentTemplate $documentTemplate
     *
     * @return void
     */
	public function restored(DocumentTemplate $documentTemplate)
    {
        if ($tenant = tenancy()->tenant) {
            \Artisan::call('es:sync', [
                'tenant' => $tenant->id
            ]);
        } else {
            \Artisan::call('es:sync');
        }
    }

    /**
     * Handle the DocumentTemplate "force deleted" event.
     *
     * @param DocumentTemplate $documentTemplate
     *
     * @return void
     */
	public function forceDeleted(DocumentTemplate $documentTemplate)
    {
        if ($tenant = tenancy()->tenant) {
            \Artisan::call('es:sync', [
                'tenant' => $tenant->id
            ]);
        } else {
            \Artisan::call('es:sync');
        }
    }
}
