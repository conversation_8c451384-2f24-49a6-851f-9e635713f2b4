<?php

namespace App\Console\Commands;

use App\Helpers\ElasticHelper;
use App\Models\Tenant;
use Illuminate\Console\Command;

class ElasticSearchSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'es:sync {tenant? : The ID of the tenant to sync (leave empty for all tenants)} {id? : The ID of the template to sync (leave empty for all templates)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Syncs templates with Elastic Search for a specific tenant or all tenants';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     */
    public function handle()
    {
        $tenantId = $this->argument('tenant');

        if ($tenantId) {
            $tenant = Tenant::find($tenantId);

            if (!$tenant) {
                $this->error("Tenant with ID '{$tenantId}' not found.");
                return Command::FAILURE;
            }

            $id = $this->argument('id');

            if($id) {
                $this->info('Starting sync for tenant: ' . $tenant->id . ' and template: ' . $id);
                $this->syncTemplate($tenant, $id);
            } else {
                $this->info('Starting full sync for tenant: ' . $tenant->id);
                $this->syncTenant($tenant);
            }

        } else {
            $this->info('Starting full sync for all tenants.');
            $tenants = Tenant::all();

            if ($tenants->isEmpty()) {
                $this->info('No tenants to sync.');
            } else {
                foreach ($tenants as $tenant) {
                    $this->info("Syncing for tenant: {$tenant->id}");
                    $this->syncTenant($tenant);
                }
            }
        }

        $this->info('Sync completed successfully.');
        return Command::SUCCESS;
    }

    /**
     * Run the sync logic for a given tenant.
     *
     * @param Tenant $tenant
     * @return void
     */
    private function syncTenant(Tenant $tenant): void
    {
        $tenant->run(function () {
            $es = new ElasticHelper();
            $es->restartIndices();
            $es->importTemplates();
        });
    }

    private function syncTemplate($tenant, $id): void {
        $tenant->run(function () use ($id) {
            $es = new ElasticHelper();
            $es->updateTemplate($id);
        });

    }
}
