<?php

namespace App\Console\Commands;

use App\Exceptions\SkipNotificationException;
use App\Models\Document;
use App\Models\Tenant;
use App\Notifications\DocumentCreated;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Notification;
use Stancl\Tenancy\Facades\Tenancy;

class SendDocumentCreatedNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sendDocumentCreatedNotifications {--tenant= : Process only specific tenant ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sends notifications for newly created documents across all tenants or specific tenant.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $tenantId = $this->option('tenant');

        if ($tenantId) {
            // Process specific tenant
            $tenant = Tenant::find($tenantId);
            if (!$tenant) {
                $this->error("Tenant with ID '{$tenantId}' not found.");
                return Command::FAILURE;
            }

            $this->info("Processing notifications for tenant: {$tenant->id}");
            return $this->processTenant($tenant);
        } else {
            // Process all tenants
            $this->info('Processing notifications for all tenants...');
            return $this->processAllTenants();
        }
    }

    /**
     * Process notifications for all tenants
     *
     * @return int
     */
    private function processAllTenants(): int
    {
        $tenants = Tenant::all();

        if ($tenants->isEmpty()) {
            $this->info('No tenants found.');
            return Command::SUCCESS;
        }

        $totalProcessed = 0;
        $errors = 0;

        foreach ($tenants as $tenant) {
            try {
                $this->info("Processing tenant: {$tenant->id}");
                $processed = $this->processTenant($tenant);

                if ($processed === Command::FAILURE) {
                    $errors++;
                    $this->error("Failed to process tenant: {$tenant->id}");
                } else {
                    $totalProcessed++;
                }
            } catch (\Exception $e) {
                $errors++;
                $this->error("Error processing tenant {$tenant->id}: " . $e->getMessage());
                report($e);
            }
        }

        $this->info("Processed {$totalProcessed} tenants successfully.");
        if ($errors > 0) {
            $this->warn("Encountered {$errors} errors during processing.");
        }

        return $errors > 0 ? Command::FAILURE : Command::SUCCESS;
    }

    /**
     * Process notifications for a specific tenant
     *
     * @param Tenant $tenant
     * @return int
     */
    private function processTenant(Tenant $tenant): int
    {
        try {
            // Initialize tenancy for the tenant
            tenancy()->initialize($tenant);

            $unsent_documents = Document::with(['user'])->whereNotNull('user_id')->where([
                'is_sent' => 0,
                'is_visible' => 1
            ])->get();

            $processed = 0;
            $errors = 0;

            foreach ($unsent_documents as $_document) {
                try {
                    // send notification if user still exists
                    if ($_document->user) {
                        $notification = new DocumentCreated($_document);
                        Notification::route('mail', $_document->user->email)->notify($notification);
                        $processed++;
                    }

                    // set flag to sent
                    $_document->is_sent = 1;
                    $_document->save();
                } catch (\Exception $e) {
                    $errors++;
                    $this->error("Error processing document {$_document->id}: " . $e->getMessage());
                    report($e);
                }
            }

            $this->info("Tenant {$tenant->id}: Processed {$processed} notifications" .
                       ($errors > 0 ? " with {$errors} errors" : ""));

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error("Failed to process tenant {$tenant->id}: " . $e->getMessage());
            report($e);
            return Command::FAILURE;
        } finally {
            // Always end tenancy context
            if (tenancy()->initialized) {
                Tenancy::end();
            }
        }
    }
}
