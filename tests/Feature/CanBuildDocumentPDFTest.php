<?php

namespace Tests\Feature;

use App\Helpers\DocumentBuilder;
use App\Models\Document;
use App\Models\Tenant;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

/**
 *
 * All existing documents for every PRODUCTION tenant must be able to render PDFs without error.
 * This test runs from a non-production environment but targets production tenant data.
 *
 * @package Tests\Feature
 */
class CanBuildDocumentPDFTest extends TestCase
{
    public function test()
    {
        // 1. Explicitly set the connection to the production landlord DB to fetch tenants.
        $production_tenants = Tenant::on('mysql_production')->get();

        // 2. Iterate over each production tenant.
        $production_tenants->each(function ($tenant) {

            // 3. Use the `run` method to initialize the tenancy context for this specific tenant.
            $tenant->run(function () use ($tenant) {

                $_doc_ref = null;

                try {
                    // check documents updated within given scope of days
                    $scope_days = 30;

                    // This DB query now automatically runs on the correct production tenant's database
                    $documents = DB::table('documents')
                        ->select('id')
                        ->whereNotNull('user_id')
                        ->whereNull('deleted_at')
                        ->where('is_visible', '=', 1)
                        ->where('updated_at', '>=', \Carbon\Carbon::now()->subDays($scope_days)->toDateTimeString())
                        ->get();


                    foreach ($documents as $_document) {
                        $_doc_ref = $_document->id;

                        $_document_to_test = Document::find($_document->id);

                        if ($_document_to_test) {
                            $builder = new DocumentBuilder($_document_to_test);
                            $builder->loadTemplate();
                        } else {
                            $this->fail("Tenant ID {$tenant->getTenantKey()}: Document with ID $_doc_ref found in query but could not be loaded as a model.");
                        }
                    }
                } catch (\Exception $e) {
                    // If an error occurs, fail the test and provide full context.
                    $this->fail("Tenant ID {$tenant->getTenantKey()}, Document ID $_doc_ref: " . $e->getMessage());
                }
            });
        });

        // If the entire loop completes without calling `$this->fail()`, the test passes.
        $this->assertTrue(true, "Successfully tested document generation for all production tenants.");
    }
}
