<?php

namespace Tests\Feature;

use App\Models\Document;
use App\Models\SectionFormModel;
use App\Models\Tenant;
use Carbon\Carbon;
use Tests\TestCase;

/**
 * Class CanRenderSectionViewsForEditableDocumentsTest
 *
 * Ensure each editable document renders section views without errors
 *
 * @package Tests\Feature
 */
class CanRenderSectionViewsForEditableDocumentsTest extends TestCase
{
	public function setUp(): void
	{
		parent::setUp();

		// Create a session store instance
		$sessionStore = app('session.store');

		// Bind the session store to the request
		$this->app['request']->setLaravelSession($sessionStore);
	}

    public function test()
    {
        // 1. Explicitly set the connection to the production landlord DB to fetch tenants.
        $production_tenants = Tenant::on('mysql_production')->get();

        // 2. Iterate over each production tenant.
        $production_tenants->each(function ($tenant) {

            // 3. Use the `run` method to initialize the tenancy context for this specific tenant.
            $tenant->run(function () use ($tenant) {
                $document = new Document();

                // check documents updated within given scope of days
                $scope_days = 30;

                $editable_documents = $document->editableDocuments()
                    ->with('template.sections', 'sectionValues')
                    ->where('updated_at', '>=', Carbon::now()->subDays($scope_days)->toDateTimeString())
                    ->get();

                if($editable_documents){
                    foreach($editable_documents as $_document){
                        foreach($_document->template->sections as $_section){

                            $sectionFormModel = new SectionFormModel($_document, $_section);
                            $sectionFormModel->setupView();

                            try{
                                view($sectionFormModel->view)->with(
                                    [
                                        'model' => $sectionFormModel,
                                        'js' => $sectionFormModel->js,
                                        'route' => route('section.store', [$document, $_section]),
                                        'enable_document_preview' => false,
                                        'should_show_wizard_tutorial' => false
                                    ]
                                )->render();
                            }
                            catch(\Throwable $e){
                                $this->fail($e->getMessage());
                            }

                        }
                    }
                }
            });

        });

        $this->assertTrue(true);

    }
}
