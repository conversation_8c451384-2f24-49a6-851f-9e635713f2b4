<?php

namespace Tests\Feature;

use App\Helpers\DocumentBuilder;
use App\Models\Document;
use App\Models\DocumentTemplate;
use App\Models\SectionFormModel;
use App\Models\Tenant;
use DOMDocument;
use Tests\TestCase;

/**
 * Class SectionAndTemplateFieldsAreSyncedTest
 *
 * Ensure all fields required for PDF generation are present inside template sections
 *
 * @package Tests\Feature
 */
class SectionAndTemplateFieldsAreSyncedTest extends TestCase
{
    private Document $document;

	public function setUp(): void
	{
		parent::setUp();

		// Create a session store instance
		$sessionStore = app('session.store');

		// Bind the session store to the request
		$this->app['request']->setLaravelSession($sessionStore);
	}

    // test if document builder template fields are synced with wizard section fields
    public function test()
    {
        foreach(Tenant::all() as $tenant) {
            // switch application context
            $tenant->run(function () use ($tenant) {
                if($templates = DocumentTemplate::where(['is_visible' => 1])->with('sections')->get()){
                    foreach($templates as $_template){
                        // create or modify document model
                        $this->handleDocument($_template);

                        // get template inputs
                        $template_inputs = $this->getTemplateInputs();

                        // get all inputs of all sections
                        $section_inputs = $this->getSectionInputs($_template);

                        if(array_diff($section_inputs, $template_inputs)){
                            $this->document->forceDelete();
                            $this->fail('Document wizard and PDF fields are not synced.');
                        }

                    }

                    if($this->document){
                        $this->document->forceDelete();
                    }

                }
            });

            $this->assertTrue(true);
        }

    }

    private function handleDocument($template): void {
        if (!isset($document)){
            $this->document = Document::create($template->id, null);
        }
        else{
            $this->document->template_id = $template->id;
            $this->document->save();
        }
    }

    private function getTemplateInputs(): array {
        $builder = new DocumentBuilder($this->document);
        $builder->loadTemplate();
        return $builder->getRequestedInputs();
    }

    private function getSectionInputs($template): array {
        $section_inputs = [];
        $ignored_input_types = ['hidden', 'submit']; // _token, submit, etc.
        $ignored_input_names = ['{ID}', '{FIELD_NAME}'];

        foreach($template->sections as $_section)
        {
            $sectionFormModel = new SectionFormModel($this->document, $_section);
            $sectionFormModel->setupView();

            try{
                $html = view($sectionFormModel->view)->with(
                    [
                        'model' => $sectionFormModel,
                        'js' => $sectionFormModel->js,
                        'route' => route('section.store', [$this->document, $_section]),
	                    'enable_document_preview' => false,
                        'should_show_wizard_tutorial' => false
                    ]
                )->render();

                $document = new DOMDocument();
                libxml_use_internal_errors(true);   // suppress invalid html element errors (html5 incompatibility)
                $document->loadHTML($html);

                // only traverse form-container scope
                $document = $document->getElementById('form-container');

                // get all inputs
                $inputs = $document->getElementsByTagName("input");

                foreach ($inputs as $input) {
                    $input_name = $input->getAttribute("name");

                    // only take the part before array markup (if multi field)
                    $input_name = trim(explode('[', $input_name)[0]);

                    if(
                        !empty($input_name) // not empty
                        && !in_array($input->getAttribute("type"), $ignored_input_types)   // input type valid
                        && !in_array($input_name, $ignored_input_names)   // input name valid
                        && !in_array($input_name, $section_inputs)  // not already saved

                    ){
                        $section_inputs[] = $input_name;
                    }
                }
            }
            catch(\Throwable $e){
                $this->fail($e->getMessage());
            }
        }

        return $section_inputs;
    }
}
