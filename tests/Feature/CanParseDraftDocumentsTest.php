<?php

namespace Tests\Feature;

use App\Helpers\DocumentParser;
use App\Models\DocumentDraft;
use App\Models\Tenant;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

/**
 *
 * All existing draft documents must be able to be parsed for editor
 *
 * @package Tests\Feature
 */
class CanParseDraftDocumentsTest extends TestCase
{
    public function test()
    {
        // 1. Explicitly set the connection to the production landlord DB to fetch tenants.
        $production_tenants = Tenant::on('mysql_production')->get();

        // 2. Iterate over each production tenant.
        $production_tenants->each(function ($tenant) {

            // 3. Use the `run` method to initialize the tenancy context for this specific tenant.
            $tenant->run(function () use ($tenant) {

                $document_model = new DocumentDraft();

                $_doc_ref = null;

                try{
                    // check documents updated within given scope of days
                    $scope_days = 30;

                    // could be lots of models, so grab ids instead and fetch models one by one
                    $documents = DB::table('document_drafts')
                        ->select('id')
                        ->whereNull('deleted_at')
                        ->where('is_visible', '=', 1)
                        ->where('updated_at', '>=', \Carbon\Carbon::now()->subDays($scope_days)->toDateTimeString())
                        ->get();


                    foreach($documents as $_document){
                        $_doc_ref = $_document->id;
                        $_document_to_test = $document_model->find($_document->id);

                        $parser = new DocumentParser(
                            $_document_to_test,
                            $_document_to_test->type_id
                        );
                    }

                }
                catch(\Exception $e)
                {
                    $this->fail("Tenant {$tenant->getTenantKey()}, Draft ID " . ($_doc_ref ?? 'n/a') . ': ' . $e->getMessage());
                }
            });

        });

        $this->assertTrue(true);
    }
}
